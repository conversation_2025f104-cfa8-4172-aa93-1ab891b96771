<!-- 代码已包�?CSS：使�?TailwindCSS , 安装 TailwindCSS 后方可看到布局样式效果 -->

<template>
  <div class="min-h-screen tech-grid bg-gray-900">
    <Logo class="absolute top-8 left-8 z-40 sm:top-8 left-4 sm:left-8 z-40 transform scale-75 sm:scale-100" />
    <!-- 使用提取出的用户信息组件 -->
    <UserInfoBar class="absolute bottom-4 sm:bottom-8 right-4 sm:right-8 z-40" :key="keyCount" />

    <!-- 返回按钮 -->
    <div class="absolute top-4 sm:top-8 right-4 sm:right-8 z-50" @click="goSetting">
      <button
        class="text-white flex items-center space-x-1 sm:space-x-2 !rounded-button whitespace-nowrap hover:opacity-80 cursor-pointer p-1 sm:p-2"
      >
        <i class="fas fa-cog text-base sm:text-lg"></i>
        <span>系统配置</span>
      </button>
    </div>

    <div
      class="container mx-auto px-2 sm:px-4 h-screen flex flex-col items-center justify-center"
    >
      <h1 class="text-2xl sm:text-3xl md:text-4xl font-bold text-white mb-8 sm:mb-16 relative">
        <span class="relative z-10">请选择识别方式</span>
        <div class="absolute inset-0 blur-lg bg-primary opacity-20"></div>
      </h1>

      <div class="flex flex-col sm:flex-row gap-4 sm:gap-8 justify-center items-center">
        <router-link
          :to="{ path: '/face', query: { loginType } }"
          v-if="
            ['FACE', 'FACE_OR_FINGERPRINT'].includes(
              loginType
            ) && loginType
          "
          class="tech-card w-64 h-64 sm:w-72 md:w-80 sm:h-72 md:h-80 flex flex-col items-center justify-center p-4 sm:p-8 rounded-lg group"
        >
          <div class="text-4xl sm:text-5xl md:text-6xl text-primary mb-4 sm:mb-6">
            <i class="fas fa-user-circle"></i>
          </div>
          <span class="text-lg sm:text-xl text-white font-mono tracking-wider"
            >人脸识别入口</span
          >
          <div
            class="mt-3 sm:mt-4 w-12 sm:w-16 h-1 bg-gradient-to-r from-primary to-secondary"
          ></div>
        </router-link>

        <router-link
          :to="{ path: '/vein', query: { loginType } }"
          v-if="
            [
              'FINGERPRINT',
              'FACE_AND_FINGERPRINT',
              'FACE_OR_FINGERPRINT',
            ].includes(loginType) && loginType
          "
          class="tech-card w-64 h-64 sm:w-72 md:w-80 sm:h-72 md:h-80 flex flex-col items-center justify-center p-4 sm:p-8 rounded-lg group"
        >
          <div class="text-4xl sm:text-5xl md:text-6xl text-primary mb-4 sm:mb-6">
            <i class="fas fa-fingerprint"></i>
          </div>
          <span class="text-lg sm:text-xl text-white font-mono tracking-wider"
            >静脉识别入口</span
          >
          <div
            class="mt-3 sm:mt-4 w-12 sm:w-16 h-1 bg-gradient-to-r from-primary to-secondary"
          ></div>
        </router-link>
      </div>

      <div class="absolute inset-0 pointer-events-none">
        <div
          class="absolute inset-0 bg-gradient-to-b from-transparent via-primary/5 to-transparent"
        ></div>
      </div>
    </div>
  </div>
</template>

<script>
import UserInfoBar from "@/components/UserInfoBar.vue";
import storage from "@/utils/storage";
import Logo from "@/components/Logo.vue";
export default {
  name: "Method-page",
  components: {
    UserInfoBar,
    Logo,
  },
  inject: [
    "speak",
    "websocketService",
    "websocketMsg",
    "wsStatus",
    "speakSync",
  ],
  data() {
    return {
      loginType: "",
      countdownConfig: 90,
      keyCount: 0,
    };
  },
  created() {


    // 从本地存储获取 loginType
    const savedLoginType = storage.local.get("loginType");
    if (savedLoginType) {
      this.loginType = savedLoginType;
    }
  },

  watch: {
    websocketMsg: {
      handler(newMessage) {
        console.log("收到新的WebSocket消息:", newMessage);
        if (newMessage) {
          this.handleTypeMessages(newMessage);
        }
      },
      deep: true,
      immediate: true,
    },
    wsStatus: {
      handler(newStatus) {
        console.log("WebSocket连接状态变化:", newStatus);
        if (newStatus === true) {
          this.getLoginOut();
          this.getLoginType();
          this.getCountDownConfig();
        }
      },
      immediate: true,
    },
    // 监听路由变化
    $route: {
      handler(to) {
        console.log("路由变化:", to.path);
        // 路由变化时重新获取登录方式
        this.getLoginOut();
        this.getLoginType();
        this.getCountDownConfig();
      },
      immediate: true,
    },
  },
  async mounted() {},
  methods: {

    async getLoginOut() {
      // 从本地存储获取用户信息
      const userInfo = storage.local.get("userInfo");
      console.log(userInfo)
      if (!userInfo) {
        console.log("未找到用户信息");
        return;
      }
      this.userInfo = userInfo;
      let order = {
        type: 10022,
        id: "getLoginOut",
        data: {
          account: this.userInfo.account,
        },
      };
      this.websocketService.send(order);
    },

    async getCountDownConfig() {
      let order = {
        type: 10301,
        id: "getCountDownConfig",
        data: {},
      };
      this.websocketService.send(order);
    },
    getLoginType() {
      //获取登录方式
      const order = {
        type: 10010,
        id: "getLoginType",
        data: {},
      };
      this.websocketService.send(order);
    },
    goSetting() {
      this.$router.push("/sysSetting");
    },
    async handleTypeMessages(message) {
      if (!message) return;
      if (message && message.messages) {
        switch (message.messages.type) {
          case 20301: // 获取倒计时配置响应
            if (message.messages.data.code === 1) {
              this.countdownConfig = message.messages.data.timeoutAlarm || 90;
              storage.local.set("countdownConfig", this.countdownConfig);
            } else {
              await this.speakSync(message.messages.data.msg);
            }
            break;
          case 20010:
            if (message.messages.data && message.messages.data.type) {
              const loginType = message.messages.data.type;
              this.loginType = loginType;
              storage.local.set("loginType", loginType);
            }
            break;
            case 20022:
              storage.local.remove("token");
              storage.local.remove("userInfo");
              storage.local.remove("faceAccount");
              storage.local.remove("veinAccount");
              this.keyCount++;
              this.$forceUpdate();
            break;  
          case 30110:
            {
              const loginType =  message.messages.data.type;
              storage.local.set("loginType", loginType);
              this.loginType = loginType;
             
            }
            break;
        }
      }
    },
  },
};
</script>

<style scoped>
@keyframes glow {
  0% {
    box-shadow: 0 0 5px #0066ff;
  }
  50% {
    box-shadow: 0 0 20px #0066ff;
  }
  100% {
    box-shadow: 0 0 5px #0066ff;
  }
}

@keyframes borderFlow {
  0% {
    border-image-source: linear-gradient(0deg, #0066ff, #00a3ff);
  }
  50% {
    border-image-source: linear-gradient(180deg, #0066ff, #00a3ff);
  }
  100% {
    border-image-source: linear-gradient(360deg, #0066ff, #00a3ff);
  }
}

.tech-card {
  background: linear-gradient(
    135deg,
    rgba(0, 102, 255, 0.1),
    rgba(0, 163, 255, 0.1)
  );
  border: 2px solid;
  border-image-slice: 1;
  border-image-source: linear-gradient(45deg, #0066ff, #00a3ff);
  transition: all 0.3s ease;
}

.tech-card:hover {
  animation: glow 2s infinite;
  transform: scale(1.02);
}

.tech-grid {
  background-image: linear-gradient(rgba(0, 102, 255, 0.1) 1px, transparent 1px),
    linear-gradient(90deg, rgba(0, 102, 255, 0.1) 1px, transparent 1px);
  background-size: 20px 20px;
}
.user-info-bar {
  background: rgba(28, 31, 55, 0.7);
  border-radius: 4px;
  padding: 8px 12px;
}

/* 添加响应式媒体查询 */
@media (max-width: 640px) {
  .tech-card {
    margin-bottom: 1rem;
  }
  
  .tech-grid {
    background-size: 15px 15px;
  }
}

@media (max-width: 500px) {
  .tech-card {
    width: 90%;
    height: 200px;
    margin-bottom: 1.5rem;
  }
  
  .tech-grid {
    background-size: 10px 10px;
  }
}
</style>

