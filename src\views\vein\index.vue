 <!-- 代码已包含 CSS：使用 TailwindCSS , 安装 TailwindCSS 后方可看到布局样式效果 -->
 <template>
  <div
    class="min-h-screen h-screen bg-gray-900 tech-grid flex items-center justify-center"
    style="overflow: hidden"
  >
    <Logo class="absolute top-8 left-8 z-40 sm:top-8 left-4 sm:left-8 z-40 transform scale-75 sm:scale-100" />
    <!-- 使用提取出的用户信息组件 -->
    <UserInfoBar class="absolute bottom-8 right-8 z-40" />

    <div class="absolute top-8 right-8 z-50 flex gap-4">
      <!-- 使用LogoutButton组件替代原来的返回登录按钮 -->
      <BackButton @click="goBack" :routeName="'Method'" />
    </div>

    <div class="w-[1440px] min-h-[1024px] relative">
      <!-- 主要内容区 -->
      <div
        class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2"
      >
        <!-- 识别框容器 -->
        <div class="relative w-[500px] h-[500px]">
          <!-- 扫描动画 -->
          <div class="absolute inset-0 border-2 border-blue-400/50">
            <div
              class="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-transparent via-blue-400 to-transparent transform animate-scan"
            ></div>
          </div>
          <!-- L型装饰 -->
          <div class="absolute top-0 left-0 w-16 h-2 bg-blue-400"></div>
          <div class="absolute top-0 left-0 w-2 h-16 bg-blue-400"></div>
          <div class="absolute top-0 right-0 w-16 h-2 bg-blue-400"></div>
          <div class="absolute top-0 right-0 w-2 h-16 bg-blue-400"></div>
          <div class="absolute bottom-0 left-0 w-16 h-2 bg-blue-400"></div>
          <div class="absolute bottom-0 left-0 w-2 h-16 bg-blue-400"></div>
          <div class="absolute bottom-0 right-0 w-16 h-2 bg-blue-400"></div>
          <div class="absolute bottom-0 right-0 w-2 h-16 bg-blue-400"></div>
          <!-- 中心识别区域 -->
          <div
            class="absolute inset-8 border border-gray-600 bg-gray-800/50 flex items-center justify-center"
          >
            <div class="text-center">
              <i class="fas fa-fingerprint text-6xl text-blue-400 mb-4"></i>
              <p class="text-gray-300 text-lg">请将手指放在采集器中</p>
            </div>
          </div>
        </div>

        <!-- 新增文字提示框 -->
        <div
          class="mt-8 w-[500px] bg-gray-800/80 backdrop-blur-sm border border-blue-400/30 rounded-lg p-4 shadow-lg transform transition-all duration-300 hover:shadow-blue-400/20"
        >
          <div class="flex items-center">
            <div
              class="flex-shrink-0 w-10 h-10 rounded-full bg-blue-500/20 flex items-center justify-center"
            >
              <i class="fas fa-info-circle text-blue-400"></i>
            </div>
            <div class="ml-4 flex-1">
              <h3 class="text-xl font-bold text-blue-300">{{ tipTitle }}</h3>
              <p
                class="mt-2 text-base text-gray-300 font-medium"
                style="color: red"
              >
                {{ tipSubMessage }}
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 使用弹窗组件 -->
    <!-- <SystemModal
      :visible.sync="showModal"
      title="操作失败"
      message="modelMsg"
      type="error"
      @close="handleModalClose"
    /> -->
  </div>
</template>
  <script>
import UserInfoBar from "@/components/UserInfoBar.vue";
import storage from "@/utils/storage";
import BackButton from "@/components/BackButton.vue";
import Logo from "@/components/Logo.vue";
// import SystemModal from '@/components/SystemModal.vue';
export default {
  name: "vein-page",
  components: {
    UserInfoBar,
    BackButton,
    Logo,
    // SystemModal
  },
  inject: ["speak", "websocketService", "websocketMsg", "speakSync"],
  watch: {
    websocketMsg: {
      handler(newMessage) {
        console.log("收到新的WebSocket消息vein:", newMessage);
        if (newMessage) {
          this.handleVeinMessages(newMessage);
        }
      },
      deep: true,
      immediate: true,
    },
  },
  created() {
    // 获取路由参数中的loginType
    this.loginType = this.$route.query.loginType;
    console.log("loginType:", this.loginType);
  },
  mounted() {
    this.startVein();
  },
  data() {
    return {
      isPlaying: false,
      modelMsg: "",
      isGoHome: false,
      loginType: "",
      statusMessage: "请将手指放在采集器中",
      veinParams: {},
      showModal: false,
      // 新增提示框文字内容
      tipTitle: "静脉识别提示",
      tipMessage: "请将手指放在采集器中",
      tipSubMessage: "识别过程中请勿频繁移动，确保手指特征清晰可见",
    };
  },
  beforeDestroy() {
    this.goBack();
  },

  methods: {
    // 显示弹窗
    openErrorModal() {
      this.showModal = true;
    },
    // 弹窗关闭回调
    handleModalClose() {
      console.log("弹窗已关闭");
      // 可以在这里处理弹窗关闭后的逻辑
    },
    startVein() {
      let order = {
        type: 10001,
        id: "startVein",
        data: {},
      };
      this.websocketService.send(order);
    },
    endVein() {
      let order = {
        type: 10021,
        id: "endVein",
        data: {},
      };

      this.websocketService.send(order);
    },
    jumpActionType(){
            if (this.isGoHome) {
              return;
            }

            var savedLoginType = storage.local.get("loginType");
            if (savedLoginType) {
              this.loginType = savedLoginType;
            }

            if (
              this.loginType === "FINGERPRINT" ||
              this.loginType === "FACE_OR_FINGERPRINT"
            ) {
              console.log(21111);
              this.$router.push({
                name: "actionType",
                params: { data: { ...this.veinParams, isVein: true } },
              });
              storage.local.set("userInfo", {
                ...this.veinParams,
                isVein: true,
              });
              console.log(1);
            }


            if(this.loginType === "FACE_AND_FINGERPRINT"){
              storage.local.set("userInfo", {
                // ...this.veinParams,
                isVein: true,
                loginType: this.loginType,
              });
              this.$router.push({
                name: "Face",
                params: {
                  data: {
                    ...this.veinParams,
                    isVein: true,
                    loginType: this.loginType,
                  },
                },
              });
         
            }

            // if (this.loginType === "FACE_AND_FINGERPRINT") {
            //   storage.local.set("veinAccount", this.veinParams.account);
            // }

            // if (
            //   this.loginType === "FACE_AND_FINGERPRINT" &&
            //   this.$route.params?.data?.isFace
            // ) {
            //   // 验证是否是同一个人
            //   const faceAccount = storage.local.get("faceAccount");

            //   console.log(
            //     "已保存指纹账号:",
            //     this.veinParams.account,
            //     faceAccount
            //   );

            //   if (faceAccount === this.veinParams.account) {
            //     console.log(3111111);
            //     console.log("")
            //     this.$router.push({
            //       name: "actionType",
            //       params: {
            //         data: {
            //           ...this.veinParams,
            //           isVein: true,
            //           loginType: this.loginType,
            //         },
            //       },
            //     });
            //     storage.local.set("userInfo", {
            //       ...this.veinParams,
            //       isVein: true,
            //       loginType: this.loginType,
            //     });
            //     storage.local.remove("veinAccount");
            //     storage.local.remove("faceAccount");
            //   } else {
            //     this.speakSync("验证失败，请确保是同一个人");
            //     this.tipSubMessage = "验证失败，请确保是同一个人";
            //     storage.local.remove("faceAccount");
            //     storage.local.remove("veinAccount");
            //     setTimeout(() => {
            //       this.$router.push({ name: "Method" });
            //     }, 2000);
            //   }
            // } else if (
            //   this.loginType === "FACE_AND_FINGERPRINT" &&
            //   !this.$route.params?.data?.isFace
            // ) {
            //   console.log(41111);
            //   this.$router.push({
            //     name: "Face",
            //     params: {
            //       data: {
            //         ...this.veinParams,
            //         isVein: true,
            //         loginType: this.loginType,
            //       },
            //     },
            //   });
            //   storage.local.set("userInfo", {
            //     ...this.veinParams,
            //     isVein: true,
            //     loginType: this.loginType,
            //   });
            // }
    
    },

    //静脉识别消息
    async handleVeinMessages(message) {
      if (!message) return;
      // 根据消息类型处理不同的情况
      if (message && message.messages) {
        switch (message.messages.type) {
          case 20001: // 请求静脉识别响应
            if (message.messages.data.code === 1) {
              this.speak(this.statusMessage);
              this.tipSubMessage = this.statusMessage;
            } else {
              this.speak(message.messages.data.msg);
              this.tipSubMessage = message.messages.data.msg;
            }

            break;

          case 30001: //静脉识别结果通知
            if (message.messages.data.code === 1) {
              this.speakSync("静脉识别通过");
              this.tipSubMessage = "静脉识别通过";
              this.veinParams = message.messages.data; //获取识别结果
              this.isGoHome = false;
              this.endVein();
              this.jumpActionType();
            } else {
              console.log("弹窗提示错误信息");
              if (!this.isPlaying) {
                this.isPlaying = true;
                this.speakSync(message.messages.data.msg);
                setTimeout(() => {
                  this.isPlaying = false;
                }, 2000); // 2秒内不重复播放
              }
              this.tipSubMessage = message.messages.data.msg;
              // this.openErrorModal = true;
            }

            break;
  

      

          default:
            console.log("未处理的消息类型:", message);
        }
      }
    },

    goBack() {
      this.isGoHome = true;
      this.endVein();
    },
    goAction() {
      this.$router.push({
        name: "actionType",
      });
    },
    // 退出登录成功的回调
    onLogoutSuccess() {
      console.log("退出登录成功，已在LogoutButton组件中处理");
      // 如果需要额外处理，可以在这里添加代码
    },
  },
};
</script>
  <style scoped>
.animate-scan {
  animation: scan 2s linear infinite;
}
@keyframes scan {
  0% {
    transform: translateY(0);
  }
  100% {
    transform: translateY(500px);
  }
}
/* 自定义输入框样式 */
input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}
.tech-grid {
  background-image: linear-gradient(rgba(0, 102, 255, 0.1) 1px, transparent 1px),
    linear-gradient(90deg, rgba(0, 102, 255, 0.1) 1px, transparent 1px);
  background-size: 20px 20px;
}
</style>
  