<template>
  <div id="app">
    <network-status-bar :offline="offline" />
    <router-view />
    <initializing-overlay :visible="isInitializing" />
  </div>
</template>

<script>
import WebSocketService from "@/utils/websocket";
import inactivityMonitor from "@/utils/inactivityMonitor"; // 引入活动监控工具
import config from "@/config";
import InitializingOverlay from "@/components/InitializingOverlay";
import NetworkStatusBar from "@/components/NetworkStatusBar";
import storage from "@/utils/storage";

export default {
  name: "App",
  components: {
    InitializingOverlay,
    NetworkStatusBar,
  },
  data() {
    return {
      messages: [],
      wsStatus: false,
      speechSynthesis: null, // 语音合成器实例
      websocketMsg: {},
      isInitializing: true, // 初始化状态标志
      times: 180000,
      offline: 1,
    };
  },
  provide() {
    return {
      websocketService: WebSocketService,
      websocketMsg: this.websocketMsg,
      speak: this.speak,
      speakSync: this.speakSync,
      handleMessage: this.handleMessage,
      wsStatus: this.wsStatus,
    };
  },
  created() {
    // 监听WebSocket消息
    storage.local.remove("loginType");
    storage.local.remove("userInfo");
    storage.local.remove("faceAccount");
    storage.local.remove("veinAccount");
    console.log("启动");
    window.addEventListener("ws-message", this.handleMessage);
    window.addEventListener("ws-close", this.handleWsClose);
    window.addEventListener("ws-error", this.handleWsError);
    window.addEventListener("ws-connect", this.handleWsConnect);
    this.initSpeechSynthesis(); // 初始化语音合成器
    this.handleConnect();

    // 监听路由变化
    this.$router.afterEach((to) => {
      // 当路由是首页时调用getSystemSettings
      if (to.path === "/" || to.name === "Method") {
        this.getSystemSettings();
      }
    });
  },

  beforeDestroy() {
    console.log("beforeDestroy");
    window.removeEventListener("ws-message", this.handleMessage);
    window.removeEventListener("ws-close", this.handleWsClose);
    window.removeEventListener("ws-error", this.handleWsError);
    window.removeEventListener("ws-connect", this.handleWsConnect);
    WebSocketService.close();

    // 停止活动监控
    inactivityMonitor.stop();
  },

  mounted() {
    // 使用异步语音播报欢迎信息
    this.speak("欢迎使用" + config.system.name);

    // 添加系统设置加载

    // 启动用户活动监控
    // inactivityMonitor.setTimeoutDuration(this.times);
    // this.startInactivityMonitor();
  },
  methods: {
    // 启动用户活动监控
    startInactivityMonitor() {
      // 设置5分钟无活动后跳转到首页
      inactivityMonitor.start(
        // 超时回调
        () => {
          console.log("自动跳转到首页");
          this.$router.push("/");
        },
        this.times, // 300000毫秒 = 5分钟
        // 警告回调 - 在超时前30秒显示
        (remainingSeconds) => {
          console.log(remainingSeconds);
          // this.$notify({
          //   title: '即将返回首页',
          //   message: `检测到长时间无操作，${remainingSeconds}秒后将自动返回首页`,
          //   type: 'warning',
          //   duration: remainingSeconds * 1000
          // });
        },
        1000 // 警告提前时间30秒
      );
    },

    async getCountDownConfig() {
      let order = {
        type: 10301,
        id: "getCountDownConfig",
        data: {},
      };
      if (WebSocketService) WebSocketService.send(order);
    },

    getLoginType() {
      //获取登录方式
      console.log("发送");
      const order = {
        type: 10010,
        id: "getLoginType",
        data: {},
      };
      if (WebSocketService) WebSocketService.send(order);
    },

    getLoad() {
      //获取登录方式
      console.log("发送");
      const order = {
        type: 10033,
        id: "getLoad",
        data: {},
      };
      if (WebSocketService) WebSocketService.send(order);
    },

    getTimeoutExit() {
      //获取获取超时退出时间
      console.log("获取获取超时退出时间");
      const order = {
        type: 10302,
        id: "timeoutExit",
        data: {},
      };
      if (WebSocketService) WebSocketService.send(order);
    },

    async getSystemSettings() {
      let order = {
        type: 10031,
        id: "getSystemSettings",
        data: {},
      };
      if (WebSocketService) WebSocketService.send(order);
    },

    // 初始化语音合成器
    initSpeechSynthesis() {
      if ("speechSynthesis" in window) {
        this.speechSynthesis = window.speechSynthesis;
      } else {
        console.warn("当前浏览器不支持语音合成");
        alert("当前浏览器不支持语音合成");
      }
    },
    // 语音播报方法（异步方式，不等待播报完成）
    speak(text) {
      if (this.speechSynthesis) {
        // 创建语音实例
        const utterance = new SpeechSynthesisUtterance(text);
        // 设置语音参数
        utterance.lang = "zh-CN"; // 设置语言为中文
        utterance.volume = 1; // 音量
        utterance.rate = 1; // 语速
        utterance.pitch = 1; // 音高
        // 开始播报
        this.speechSynthesis.cancel();
        this.speechSynthesis.speak(utterance);
      }
    },

    // 同步语音播报方法（返回Promise，等待播报完成）
    speakSync(text) {
      return new Promise((resolve) => {
        if (this.speechSynthesis) {
          // 创建语音实例
          const utterance = new SpeechSynthesisUtterance(text);
          // 设置语音参数
          utterance.lang = "zh-CN"; // 设置语言为中文
          utterance.volume = 1; // 音量
          utterance.rate = 1; // 语速
          utterance.pitch = 1; // 音高

          // 监听播报结束事件
          utterance.onend = () => {
            resolve(); // 播报结束后解析Promise
          };

          // 监听错误事件，防止Promise永远不解析
          utterance.onerror = () => {
            console.warn("语音合成出错");
            resolve(); // 出错时也解析Promise
          };

          // 开始播报
          this.speechSynthesis.cancel();
          this.speechSynthesis.speak(utterance);
        } else {
          // 如果不支持语音合成，直接解析Promise
          resolve();
        }
      });
    },

    handleConnect() {
      // 从配置文件中获取WebSocket连接信息
      const wsConfig = config.websocket;

      // 设置WebSocket配置参数
      WebSocketService.setConfig({
        pingInterval: wsConfig.pingInterval,
        maxReconnectCount: wsConfig.maxReconnectCount,
      });

      // 连接WebSocket
      const connected = WebSocketService.connect(wsConfig.url, wsConfig.params);
      console.log("connected", connected);
      if (connected) {
        this.wsStatus = true;
      }
    },

    handleSend() {
      const testMessage = {
        type: "message",
        content: "测试消息app",
        timestamp: Date.now(),
      };

      const sent = WebSocketService.send(testMessage);
      if (sent) {
        this.messages.push(`发送: ${JSON.stringify(testMessage)}`);
      }
    },

    handleClose() {
      WebSocketService.close();
      this.status = false;
    },

    async handleMessage(event) {
      const message = event.detail;

      // 更新 websocketMsg 中的消息
      this.$set(this.websocketMsg, "messages", message);
      
      // 处理初始化状态
      if (message && message.data && message.type == 20033) {
        if (message.data && message.data.code === 1) {
          this.isInitializing = false;
        } else {
          this.isInitializing = true;
        }
      }

      // 处理倒计时配置
      if (message && message.data && message.type == 20301) {
        if (message.data && message.data.code === 1) {
          this.countdownConfig = message.data.timeoutAlarm || 90;
          storage.local.set("countdownConfig", this.countdownConfig);
        } else {
          await this.speakSync(message.data.msg);
        }
      }

      // 处理登录类型
      if (message && message.data && message.type == 10010) {
        const loginType = message.data.type;
        storage.local.set("loginType", loginType);
        this.loginType = loginType;
        this.$router.push({ name: "Method" });
      }

      // 处理超时退出
      if (message && message.data && message.type == 20302) {
        console.log('message.data.timeoutExit',message.data.timeoutExit)
         this.times = message.data.timeoutExit * 1000 || 300000;
          //  this.times = message.data.timeoutExit * 1000;
        inactivityMonitor.setTimeoutDuration(this.times);
        this.startInactivityMonitor();
      }

      // 处理离线状态
      if (message && message.data && message.type == 20031) {
        this.offline = message.data.offline;
      }
    },

    // WebSocket事件处理函数
    handleWsClose() {
      this.wsStatus = false;
      this.isInitializing = true;
      this.$router.push("/");
    },

    handleWsError() {
      this.wsStatus = false;
      this.isInitializing = true;
      this.$router.push("/");
    },

    handleWsConnect() {
      setTimeout(() => {
        this.getTimeoutExit();
        this.getLoad();
        this.getLoginType();
        this.getCountDownConfig();
        this.getSystemSettings(); // 添加系统设置加载
      }, 500);
      this.wsStatus = true;
    },
  },
};
</script>

<style>
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html,
body {
  /* app-region: drag; */
  /* -webkit-app-region: drag; */
  /* user-select: none; */
  height: 100%;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen,
    Ubuntu, Cantarell, "Open Sans", "Helvetica Neue", sans-serif;
  font-size: 16px; /* 设置默认字体大小为16px */
}

/* 设置根元素默认字体大小 */
:root {
  font-size: 16px;
}

#app {
  height: 100vh;
  background-color: #1a1f2c;
  color: white;
  width: 100vw;
  overflow-x: hidden;
}

.titlebar {
  height: 30px;
  background: blue;
  color: white;
  display: flex;
  justify-content: center;
  align-items: center;
}

/* 滚动条整体美化 - 科技感风格 */
::-webkit-scrollbar {
  width: 12px;
  height: 12px;
}

/* 滚动条轨道 */
::-webkit-scrollbar-track {
  background: #1a1f2c;
  border-radius: 10px;
}

/* 滚动条滑块 */
::-webkit-scrollbar-thumb {
  background: #212840;
  border-radius: 10px;
  border: 2px solid #1a1f2c;
}

/* 鼠标悬停在滑块上的效果 */
::-webkit-scrollbar-thumb:hover {
  background: #252e4a;
}

/* 滚动条角落 */
::-webkit-scrollbar-corner {
  background: #1a1f2c;
}

/* 自适应屏幕分辨率 */
@media screen and (max-width: 1366px) {
  :root {
    font-size: 16px;
  }
}

@media screen and (max-width: 1024px) {
  :root {
    font-size: 15px;
  }
}

@media screen and (max-width: 768px) {
  :root {
    font-size: 14px;
  }
}
</style> 