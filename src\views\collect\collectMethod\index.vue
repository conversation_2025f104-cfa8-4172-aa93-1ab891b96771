<!-- 代码已包�?CSS：使�?TailwindCSS , 安装 TailwindCSS 后方可看到布局样式效果 -->

<template>
  <div class="min-h-screen tech-grid bg-gray-900">
    <Logo class="absolute top-8 left-8 z-40 sm:top-8 left-4 sm:left-8 z-40 transform scale-75 sm:scale-100" />
    <!-- 使用提取出的用户信息组件 -->
    <UserInfoBar class="absolute bottom-2 sm:bottom-4 md:bottom-8 right-2 sm:right-4 md:right-8 z-40" />

    <!-- 返回按钮 -->
    <div class="absolute top-2 sm:top-4 md:top-8 right-2 sm:right-4 md:right-8 z-50 flex gap-4">
      <!-- 使用LogoutButton组件替代原来的返回登录按钮 -->
      <BackButton />
    </div>

    <div
      class="container mx-auto px-2 sm:px-4 h-screen flex flex-col items-center justify-center"
    >
      <!-- 离线模式提示 -->
      <div class="notification-banner mb-4 sm:mb-6 md:mb-8 text-center w-full max-w-xs sm:max-w-sm md:max-w-lg">
        <div
          class="px-3 sm:px-4 md:px-6 py-2 sm:py-3 md:py-4 rounded-lg bg-gradient-to-r from-blue-900/50 to-indigo-900/50 backdrop-blur-sm border border-blue-500/30"
        >
          <div class="flex items-center justify-center mb-1 sm:mb-2">
            <i class="fas fa-info-circle text-blue-400 mr-2"></i>
            <span class="text-blue-300 font-semibold text-sm sm:text-base">用户生物信息录入！</span>
          </div>
          <p class="text-blue-200 text-xs sm:text-sm">
            当前为离线模式，数据仅本地存储，联网后用户信息将自动同步平台。
          </p>
        </div>
      </div>

      <h1 class="text-2xl sm:text-3xl md:text-4xl font-bold text-white mb-8 sm:mb-12 md:mb-16 relative">
        <span class="relative z-10">请选择采集方式</span>
        <div class="absolute inset-0 blur-lg bg-primary opacity-20"></div>
      </h1>

      <div class="flex flex-col sm:flex-row gap-4 sm:gap-6 md:gap-8 justify-center items-center">
        <router-link
          :to="{ path: '/collectFace', query: {} }"
          class="tech-card w-64 sm:w-72 md:w-80 h-64 sm:h-72 md:h-80 flex flex-col items-center justify-center p-4 sm:p-6 md:p-8 rounded-lg group"
        >
          <div class="text-4xl sm:text-5xl md:text-6xl text-primary mb-4 sm:mb-5 md:mb-6">
            <i class="fas fa-user-circle"></i>
          </div>
          <span class="text-lg sm:text-xl text-white font-mono tracking-wider"
            >人脸采集</span
          >
          <div
            class="mt-3 sm:mt-4 w-12 sm:w-16 h-1 bg-gradient-to-r from-primary to-secondary"
          ></div>
        </router-link>

        <router-link
          :to="{ path: '/collectVein', query: {} }"
          class="tech-card w-64 sm:w-72 md:w-80 h-64 sm:h-72 md:h-80 flex flex-col items-center justify-center p-4 sm:p-6 md:p-8 rounded-lg group"
        >
          <div class="text-4xl sm:text-5xl md:text-6xl text-primary mb-4 sm:mb-5 md:mb-6">
            <i class="fas fa-fingerprint"></i>
          </div>
          <span class="text-lg sm:text-xl text-white font-mono tracking-wider"
            >静脉采集</span
          >
          <div
            class="mt-3 sm:mt-4 w-12 sm:w-16 h-1 bg-gradient-to-r from-primary to-secondary"
          ></div>
        </router-link>
      </div>

      <div class="absolute inset-0 pointer-events-none">
        <div
          class="absolute inset-0 bg-gradient-to-b from-transparent via-primary/5 to-transparent"
        ></div>
      </div>
    </div>
  </div>
</template>

<script>
import UserInfoBar from "@/components/UserInfoBar.vue";

import Logo from "@/components/Logo.vue";
import BackButton from "@/components/BackButton.vue";
export default {
  name: "Method-page",
  components: {
    UserInfoBar,
    Logo,
    BackButton,
  },
  inject: [
    "speak",
    "websocketService",
    "websocketMsg",
    "wsStatus",
    "speakSync",
  ],
  data() {
    return {
      loginType: "",
    };
  },
  created() {},

  watch: {
    websocketMsg: {
      handler(newMessage) {
        console.log("收到新的WebSocket消息:", newMessage);
        if (newMessage) {
          this.handleTypeMessages(newMessage);
        }
      },
      deep: true,
      immediate: true,
    },
    wsStatus: {
      handler(newStatus) {
        console.log("WebSocket连接状态变化:", newStatus);
        if (newStatus === true) {
          this.getLoginType();
        }
      },
      immediate: true,
    },
  },
  async mounted() {},
  methods: {
    getLoginType() {
      //获取登录方式
      const order = {
        type: 10010,
        id: "getLoginType",
        data: {},
      };
      this.websocketService.send(order);
    },
    goSetting() {
      this.$router.push("/sysSetting");
    },
    handleTypeMessages(message) {
      if (!message) return;
      if (message && message.messages) {
        switch (message.messages.type) {
          case 20010:  //主动请求
          
            if (message.messages.data && message.messages.data.type) {
              const loginType = message.messages.data.type;
              this.loginType = loginType;
            }
            break;
            case 30110: //发生改变触发
            if (message.messages.data && message.messages.data.type) {
              const loginType = message.messages.data.type;
              this.loginType = loginType;
            }
            break;  
        }
      }
    },
  },
};
</script>

<style scoped>
@keyframes glow {
  0% {
    box-shadow: 0 0 5px #0066ff;
  }
  50% {
    box-shadow: 0 0 20px #0066ff;
  }
  100% {
    box-shadow: 0 0 5px #0066ff;
  }
}

@keyframes borderFlow {
  0% {
    border-image-source: linear-gradient(0deg, #0066ff, #00a3ff);
  }
  50% {
    border-image-source: linear-gradient(180deg, #0066ff, #00a3ff);
  }
  100% {
    border-image-source: linear-gradient(360deg, #0066ff, #00a3ff);
  }
}

.tech-card {
  background: linear-gradient(
    135deg,
    rgba(0, 102, 255, 0.1),
    rgba(0, 163, 255, 0.1)
  );
  border: 2px solid;
  border-image-slice: 1;
  border-image-source: linear-gradient(45deg, #0066ff, #00a3ff);
  transition: all 0.3s ease;
}

.tech-card:hover {
  animation: glow 2s infinite;
  transform: scale(1.02);
}

.tech-grid {
  background-image: linear-gradient(rgba(0, 102, 255, 0.1) 1px, transparent 1px),
    linear-gradient(90deg, rgba(0, 102, 255, 0.1) 1px, transparent 1px);
  background-size: 20px 20px;
}
.user-info-bar {
  background: rgba(28, 31, 55, 0.7);
  border-radius: 4px;
  padding: 8px 12px;
}

/* 添加响应式媒体查询 */
@media (max-width: 640px) {
  .tech-card {
    margin-bottom: 1rem;
  }
}

@media (min-width: 1600px) {
  .container {
    max-width: 1536px;
  }
}

@media (max-width: 360px) {
  .tech-card {
    width: 90%;
    height: 200px;
  }
}
</style>

