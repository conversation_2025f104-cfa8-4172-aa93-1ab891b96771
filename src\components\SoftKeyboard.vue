<template>
  <div
    class="soft-keyboard-toggle"
    :style="{ left: position.x + 'px', top: position.y + 'px' }"
    @mousedown="startDrag"
    @touchstart="startDrag"
  >
    <!-- 软键盘切换按钮 -->
    <el-button
      @click="handleButtonClick"
      :type="keyboardVisible ? 'danger' : 'primary'"
      size="medium"
      :icon="keyboardVisible ? 'el-icon-close' : 'el-icon-edit'"
      class="keyboard-toggle-btn"
      circle
    >
      {{ keyboardVisible ? '隐藏' : '显示' }}
    </el-button>
  </div>
</template>

<script>
export default {
  name: 'SoftKeyboard',
  props: {
    // 是否启用快捷键
    enableShortcuts: {
      type: Boolean,
      default: true
    },
    // 是否在初始化时自动显示软键盘
    autoInitialize: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      keyboardVisible: false,
      // 拖拽相关数据
      isDragging: false,
      hasDragged: false,
      dragStartTime: 0,
      position: {
        x: window.innerWidth - 90, // 初始位置：右下角
        y: window.innerHeight - 190
      },
      dragStart: {
        x: 0,
        y: 0
      },
      initialPosition: {
        x: 0,
        y: 0
      },
      elementSize: {
        width: 60,
        height: 60
      }
    }
  },
  mounted() {
    // 初始化快捷键监听
    if (this.enableShortcuts) {
      this.initShortcuts()
    }

    // 如果设置了自动初始化，则显示软键盘
    if (this.autoInitialize) {
      this.showKeyboard()
    }

    // 初始化拖拽事件监听
    this.initDragEvents()

    // 监听窗口大小变化，确保组件不超出屏幕
    this.resizeHandler = () => {
      this.constrainPosition()
    }
    window.addEventListener('resize', this.resizeHandler)

    // 初始化位置约束
    this.$nextTick(() => {
      this.constrainPosition()
    })
  },
  beforeDestroy() {
    // 清理快捷键监听
    if (this.enableShortcuts) {
      this.removeShortcuts()
    }

    // 确保在组件销毁时隐藏软键盘
    if (this.keyboardVisible) {
      this.hideKeyboard()
    }

    // 清理拖拽事件监听
    this.removeDragEvents()

    // 清理窗口大小变化监听
    if (this.resizeHandler) {
      window.removeEventListener('resize', this.resizeHandler)
    }
  },
  methods: {
    /**
     * 显示软键盘
     */
    showKeyboard() {
      try {
        if (!this.keyboardVisible) {
          // 调用Electron API显示软键盘
          if (window.electronAPI && window.electronAPI.openOsk) {
            window.electronAPI.openOsk()
            this.keyboardVisible = true
            this.$emit('keyboard-show')
            this.$emit('keyboard-change', true)
            
            console.log('软键盘显示成功')
          } else {
            throw new Error('Electron API不可用')
          }
        }
      } catch (error) {
        console.error('显示软键盘失败:', error)
        this.$message.error('显示软键盘失败: ' + error.message)
        this.$emit('keyboard-error', error)
      }
    },
    
    /**
     * 隐藏软键盘
     */
    hideKeyboard() {
      try {
        if (this.keyboardVisible) {
          // 调用Electron API隐藏软键盘
          if (window.electronAPI && window.electronAPI.closeOsk) {
            window.electronAPI.closeOsk()
            this.keyboardVisible = false
            this.$emit('keyboard-hide')
            this.$emit('keyboard-change', false)
            
            console.log('软键盘隐藏成功')
          } else {
            throw new Error('Electron API不可用')
          }
        }
      } catch (error) {
        console.error('隐藏软键盘失败:', error)
        this.$message.error('隐藏软键盘失败: ' + error.message)
        this.$emit('keyboard-error', error)
      }
    },
    
    /**
     * 切换软键盘显示状态
     */
    toggleKeyboard() {
      if (this.keyboardVisible) {
        this.hideKeyboard()
      } else {
        this.showKeyboard()
      }
    },
    
    /**
     * 初始化快捷键监听
     */
    initShortcuts() {
      this.shortcutHandler = (event) => {
        // Ctrl+K: 切换软键盘
        if (event.ctrlKey && event.key === 'k' && !event.shiftKey && !event.altKey) {
          event.preventDefault()
          this.toggleKeyboard()
        }
      }
      
      document.addEventListener('keydown', this.shortcutHandler)
    },
    
    /**
     * 移除快捷键监听
     */
    removeShortcuts() {
      if (this.shortcutHandler) {
        document.removeEventListener('keydown', this.shortcutHandler)
        this.shortcutHandler = null
      }
    },

    /**
     * 处理按钮点击事件
     */
    handleButtonClick(event) {
      // 如果刚刚进行了拖拽，则不触发点击事件
      if (this.isDragging || this.hasDragged) {
        event.preventDefault()
        event.stopPropagation()
        this.hasDragged = false
        return
      }

      this.toggleKeyboard()
    },

    /**
     * 开始拖拽
     */
    startDrag(event) {
      // 阻止默认行为
      event.preventDefault()

      this.isDragging = true
      this.hasDragged = false
      this.dragStartTime = Date.now()

      // 获取初始鼠标/触摸位置
      const clientX = event.type === 'touchstart' ? event.touches[0].clientX : event.clientX
      const clientY = event.type === 'touchstart' ? event.touches[0].clientY : event.clientY

      this.dragStart = {
        x: clientX - this.position.x,
        y: clientY - this.position.y
      }

      this.initialPosition = {
        x: clientX,
        y: clientY
      }

      // 添加临时样式
      document.body.style.userSelect = 'none'
      document.body.style.cursor = 'grabbing'
    },

    /**
     * 拖拽中
     */
    onDrag(event) {
      if (!this.isDragging) return

      event.preventDefault()

      // 获取当前鼠标/触摸位置
      const clientX = event.type === 'touchmove' ? event.touches[0].clientX : event.clientX
      const clientY = event.type === 'touchmove' ? event.touches[0].clientY : event.clientY

      // 检测是否真的在拖拽（移动距离超过阈值）
      const deltaX = Math.abs(clientX - this.initialPosition.x)
      const deltaY = Math.abs(clientY - this.initialPosition.y)
      const dragThreshold = 5 // 像素阈值

      if (deltaX > dragThreshold || deltaY > dragThreshold) {
        this.hasDragged = true
      }

      // 计算新位置
      let newX = clientX - this.dragStart.x
      let newY = clientY - this.dragStart.y

      // 约束位置不超出屏幕
      const maxX = window.innerWidth - this.elementSize.width
      const maxY = window.innerHeight - this.elementSize.height

      newX = Math.max(0, Math.min(newX, maxX))
      newY = Math.max(0, Math.min(newY, maxY))

      this.position = { x: newX, y: newY }
    },

    /**
     * 结束拖拽
     */
    endDrag() {
      if (!this.isDragging) return

      this.isDragging = false

      // 移除临时样式
      document.body.style.userSelect = ''
      document.body.style.cursor = ''

      // 确保位置在屏幕范围内
      this.constrainPosition()
    },

    /**
     * 约束位置在屏幕范围内
     */
    constrainPosition() {
      const maxX = window.innerWidth - this.elementSize.width
      const maxY = window.innerHeight - this.elementSize.height

      this.position.x = Math.max(0, Math.min(this.position.x, maxX))
      this.position.y = Math.max(0, Math.min(this.position.y, maxY))
    },

    /**
     * 初始化拖拽事件监听
     */
    initDragEvents() {
      this.dragHandler = this.onDrag.bind(this)
      this.dragEndHandler = this.endDrag.bind(this)

      // 鼠标事件
      document.addEventListener('mousemove', this.dragHandler)
      document.addEventListener('mouseup', this.dragEndHandler)

      // 触摸事件
      document.addEventListener('touchmove', this.dragHandler, { passive: false })
      document.addEventListener('touchend', this.dragEndHandler)
    },

    /**
     * 移除拖拽事件监听
     */
    removeDragEvents() {
      if (this.dragHandler) {
        document.removeEventListener('mousemove', this.dragHandler)
        document.removeEventListener('touchmove', this.dragHandler)
        this.dragHandler = null
      }

      if (this.dragEndHandler) {
        document.removeEventListener('mouseup', this.dragEndHandler)
        document.removeEventListener('touchend', this.dragEndHandler)
        this.dragEndHandler = null
      }
    }
  }
}
</script>

<style scoped>
.soft-keyboard-toggle {
  position: fixed;
  z-index: 9999;
  cursor: grab;
  user-select: none;
  touch-action: none;
}

.soft-keyboard-toggle:active {
  cursor: grabbing;
}

.keyboard-toggle-btn {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  transition: all 0.3s ease;
  font-size: 12px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 2px;
}

.keyboard-toggle-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
}

.keyboard-toggle-btn i {
  font-size: 16px;
  margin: 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .keyboard-toggle-btn {
    width: 50px;
    height: 50px;
    font-size: 10px;
  }

  .keyboard-toggle-btn i {
    font-size: 14px;
  }
}

/* 动画效果 */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: scale(0.8);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

.soft-keyboard-toggle {
  animation: fadeIn 0.3s ease-out;
}
</style> 