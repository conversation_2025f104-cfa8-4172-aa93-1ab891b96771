<!-- 代码已包含 CSS：使用 TailwindCSS , 安装 TailwindCSS 后方可看到布局样式效果 -->
<template>
  <div
    class="min-h-screen h-screen bg-gray-900 tech-grid flex items-center justify-center"
    style="overflow: hidden"
  >
    <Logo class="absolute top-8 left-8 z-40 sm:top-8 left-4 sm:left-8 z-40 transform scale-75 sm:scale-100" />
    <!-- 使用提取出的用户信息组件 -->
    <UserInfoBar class="absolute bottom-8 right-8 z-40" />

    <div class="absolute top-8 right-8 z-50 flex gap-4">
      <!-- 使用LogoutButton组件替代原来的返回登录按钮 -->
      <BackButton />
    </div>

    <div class="w-[1440px] min-h-[1024px] relative">
      <!-- 主要内容区 -->
      <div
        class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2"
      >
        <!-- 识别框容器 -->
        <div class="relative w-[500px] h-[500px]">
          <!-- 扫描动画 -->
          <div class="absolute inset-0 border-2 border-blue-400/50">
            <div
              class="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-transparent via-blue-400 to-transparent transform animate-scan"
            ></div>
          </div>
          <!-- L型装饰 -->
          <div class="absolute top-0 left-0 w-16 h-2 bg-blue-400"></div>
          <div class="absolute top-0 left-0 w-2 h-16 bg-blue-400"></div>
          <div class="absolute top-0 right-0 w-16 h-2 bg-blue-400"></div>
          <div class="absolute top-0 right-0 w-2 h-16 bg-blue-400"></div>
          <div class="absolute bottom-0 left-0 w-16 h-2 bg-blue-400"></div>
          <div class="absolute bottom-0 left-0 w-2 h-16 bg-blue-400"></div>
          <div class="absolute bottom-0 right-0 w-16 h-2 bg-blue-400"></div>
          <div class="absolute bottom-0 right-0 w-2 h-16 bg-blue-400"></div>
          <!-- 中心识别区域 -->
          <div
            class="absolute inset-8 border border-gray-600 bg-gray-800/50 flex flex-col items-center justify-center"
          >
            <div class="text-center flex flex-col items-center justify-center">
              <!-- <i class="fas fa-fingerprint text-6xl text-blue-400 mb-4"></i> -->

              <!-- <img
               class="text-6xl text-blue-400 mb-4 mx-auto"
                :src="require(`@/assets/finger${recordIndex}.png`)"
                alt=""
              /> -->

              <img
                v-if="fingerImageUrl  && !isCollecting"
                :src="fingerImageUrl"
                alt="已上传图像"
              />

              <img
                v-if="fingerImageUrl && isCollecting"
                :src="require(`@/assets/finger${recordIndex}.png`)"
                alt=""
              />
              <p class="text-gray-300 text-lg mb-4">{{ statusMessage }}</p>
            </div>
          </div>
        </div>

        <!-- 采集过程消息提示 - 移到识别框外部 -->
        <div class="mt-6 flex flex-col items-center">
          <div v-if="collectingStatus" class="message-box mb-4">
            {{ collectingStatus }}
          </div>

          <!-- 按钮区域 - 水平布局 -->
          <div class="flex flex-row gap-4">
            <!-- 开始采集按钮 -->
            <button
              @click="handleStartCollect"
              class="collect-button"
              :class="{ collecting: isCollecting }"
              :disabled="isCollecting"
            >
              <span v-if="isCollecting" class="flex items-center">
                <svg
                  class="animate-spin -ml-1 mr-2 h-4 w-4 text-white"
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 24 24"
                >
                  <circle
                    class="opacity-25"
                    cx="12"
                    cy="12"
                    r="10"
                    stroke="currentColor"
                    stroke-width="4"
                  ></circle>
                  <path
                    class="opacity-75"
                    fill="currentColor"
                    d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                  ></path>
                </svg>
                采集中...
              </span>
              <span v-else>开始采集</span>
            </button>
    
            <!-- 比对按钮 -->
            <button @click="handleCompare" v-if="fingerCharaData" class="compare-button">
              开始比对
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import UserInfoBar from "@/components/UserInfoBar.vue";
import storage from "@/utils/storage";
import BackButton from "@/components/BackButton.vue";
import Logo from "@/components/Logo.vue";
export default {
  name: "vein-page",
  components: {
    UserInfoBar,
    BackButton,
    Logo,
  },
  inject: ["speak", "websocketService", "websocketMsg", "speakSync"],
  watch: {
    websocketMsg: {
      handler(newMessage) {
        console.log("收到新的WebSocket消息vein:", newMessage);
        if (newMessage) {
          this.handleVeinMessages(newMessage);
        }
      },
      deep: true,
      immediate: true,
    },
  },
  created() {
    // 获取路由参数中的loginType
   this.getConent();
  },
  mounted() {

  },
  data() {
    return {
      isButtonDisabled:false,
      fingerCharaData:'',
      fingerImageUrl:require(`@/assets/finger0.png`),
      recordIndex:0,
      isGoHome: false,
      loginType: "",
      statusMessage: "请将手指放在采集器中",
      veinParams: {},
      isCollecting: false,
      collectingStatus: null,
      veinCollected: false,
    };
  },

  methods: {

    async getConent(){
      const currUser = await storage.local.get('currsorEditUser');
      let order = {
        type: 10020,
        id: "getConent",
        data: {
          userId: Number(currUser.id),
          type: 1,
        },
      };
      this.websocketService.send(order);
    },
   async startVein() {
      // const userInfo = await storage.local.get('userInfo');
      const currUser = await storage.local.get('currsorEditUser');
      let order = {
        type: 10016,
        id: "starCollectVein",
        data: {
          account: currUser?.account,
          username: currUser?.account,
        },
      }
      this.websocketService.send(order);
    },
    endVein() {
      let order = {
        type: 10021,
        id: "endVein",
        data: {},
      };

      this.websocketService.send(order);
    },

    handleCompareVein(fingerChara) {
      // 请求静脉对比
  
      const order = {
        type: 10018,
        id: "compareVein",
        data: {
          fingerChara
        }
      };
      this.websocketService.send(order);
    },

    // 处理开始采集按钮点击
    handleStartCollect() {
      if (this.isCollecting) return; // 防止重复点击

      this.isCollecting = true;
      this.collectingStatus = "开始静脉采集，请保持手指放在采集器上";
      this.speak("开始静脉采集，请保持手指放在采集器上");

      // 短暂延迟后开始采集，让用户有时间准备
      setTimeout(() => {
        this.startVein();
      }, 1000);
    },

    // 处理比对按钮点击
    handleCompare() {
      this.collectingStatus = "开始静脉比对，请保持手指不要移动...";
      this.speak("开始静脉比对，请保持手指不要移动");
       this.handleCompareVein(this.fingerCharaData);
    },

   async saveVein(content){
      const currUser = await storage.local.get('currsorEditUser');
      console.log("currUser",currUser)
      let order = {
        type: 10019, // 
        id: "saveVein",
        data: {
          userId: Number(currUser.id),
          type: 1,
          content
        },
      };
      this.websocketService.send(order);
    },

    //静脉识别消息
    async handleVeinMessages(message) {
      if (!message) return;
      // 根据消息类型处理不同的情况
      if (message && message.messages) {
        var messages = message.messages
        switch (message.messages.type) {
          case 20016: // 静脉采集响应
         
          if (messages.data.code === 1) {
            this.collectingStatus = "设备初始化成功";
            this.wsStatus = true;
            this.isCollecting = true;
            this.collectingStatus = "请将手指放在采集器中";
          } else {
            this.collectingStatus = messages.data.msg;
            this.wsStatus = false;
            this.isButtonDisabled = false; // 启用按钮
            this.isCollecting = false;
          }
          break;
     

        case 20018: // 对比响应
          if (messages.data.code === 1) {
            this.collectingStatus = "指静脉对比成功";
            this.saveVein(messages.data.fingerChara);
          } else {
            this.collectingStatus = messages.data.msg;
            this.wsStatus = false;
            this.isCollecting = false;
          }
          break;

          case 20019: // 采集成功
          if (messages.data.code === 1) {
            this.collectingStatus = "采集成功";
            this.isCollecting = false;
            this.endVein();
          } else {
            this.collectingStatus = messages.data.msg;
            this.wsStatus = false;
            this.isCollecting = false;
          }
          break; 
          case 20020: // 采集成功
          if (messages.data.code === 1) {
             this.fingerCharaData = messages.data.content || '';
             this.fingerImageUrl =  messages.data.content  ? require(`@/assets/finger3.png`):require(`@/assets/finger0.png`);
          } else {
            this.fingerCharaData =  '';
            this.collectingStatus = messages.data.msg;
            this.wsStatus = false;
            this.isCollecting = false;
          }
          break;

          case 20021: // 成功跳转
          if (messages.data.code === 1) {
            this.collectingStatus = messages.data.msg;
            this.isCollecting = false;
            // 跳转到用户管理页面
            // this.$router.push('/userManage');
            this.getConent();
          } else {
            this.collectingStatus = messages.data.msg;
            this.isCollecting = false;
          }
          break; 

        case 30161: // 通知静脉序号
          if (messages.data.code  == 1) {
            this.collectingStatus = messages.data.msg;
            if (messages.data.index == 2) {
              this.isCollecting = true;
            }
            this.recordIndex = messages.data.index;
            this.collectingStatus = "请将手指放在采集器中";
          } else {
            this.collectingStatus = messages.data.msg;
            this.isCollecting = false;
          }
          break;
        case 30162: // 通知移除手指
          if (messages.data.code  == 1) {
            this.collectingStatus = "请将手指移开采集器采集器";
          } else {
            this.collectingStatus = messages.data.msg;
            this.isCollecting = false;
          }
          break;
        case 30163: // 通知静脉特征
          if (messages.data.code  == 1) {
            this.collectingStatus = messages.data.msg;
            this.fingerCharaData = messages.data.fingerChara;
            this.handleCompareVein(this.fingerCharaData);
          } else {
         
            this.collectingStatus = message.data.msg;
            this.isCollecting = false;
          }
          break;
        case 30164: // 通知异常
          if (messages.data.code  !== 1) {
            this.collectingStatus = messages.data.msg;
            this.recordIndex = 0;
            this.isCollecting = false;
          }
          break;
        default:
          break;
        }
      }
    },

    goBack() {
      this.isGoHome = true;
      this.endVein();
      this.$router.push("/userManage");
    },
    beforeDestroy(){
      this.isGoHome = true;
      this.endVein();
    },
    goAction() {
      this.$router.push({
        name: "actionType",
      });
    },
    // 退出登录成功的回调
    onLogoutSuccess() {
      console.log("退出登录成功，已在LogoutButton组件中处理");
      // 如果需要额外处理，可以在这里添加代码
    },

    // 比对成功后的导航方法
    navigateAfterSuccess(){}
  },
};
</script>
<style scoped>
.animate-scan {
  animation: scan 2s linear infinite;
}
@keyframes scan {
  0% {
    transform: translateY(0);
  }
  100% {
    transform: translateY(500px);
  }
}

/* 消息框样式 */
.message-box {
  background-color: rgba(30, 64, 175, 0.5);
  border: 1px solid rgba(59, 130, 246, 0.5);
  color: #bfdbfe;
  padding: 10px 16px;
  border-radius: 8px;
  margin-bottom: 16px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1),
    0 2px 4px -1px rgba(0, 0, 0, 0.06);
  max-width: 90%;
  text-align: center;
  animation: fadeIn 0.3s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(5px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 采集按钮样式 */
.collect-button {
  background: linear-gradient(to right, #2563eb, #3b82f6);
  color: white;
  font-weight: bold;
  padding: 10px 20px;
  border-radius: 6px;
  transition: all 0.3s ease;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 120px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1),
    0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.collect-button:hover:not(:disabled) {
  background: linear-gradient(to right, #1d4ed8, #2563eb);
  box-shadow: 0 6px 8px -2px rgba(0, 0, 0, 0.15),
    0 3px 6px -2px rgba(0, 0, 0, 0.1);
  transform: translateY(-1px);
}

.collect-button:active:not(:disabled) {
  transform: translateY(0);
  box-shadow: 0 2px 4px -1px rgba(0, 0, 0, 0.1),
    0 1px 2px -1px rgba(0, 0, 0, 0.06);
}

.collecting {
  background: linear-gradient(to right, #1e40af, #3b82f6);
  cursor: not-allowed;
  opacity: 0.8;
}

/* 自定义输入框样式 */
input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}
.tech-grid {
  background-image: linear-gradient(rgba(0, 102, 255, 0.1) 1px, transparent 1px),
    linear-gradient(90deg, rgba(0, 102, 255, 0.1) 1px, transparent 1px);
  background-size: 20px 20px;
}

/* 比对按钮样式 */
.compare-button {
  background: linear-gradient(to right, #059669, #10b981);
  color: white;
  font-weight: bold;
  padding: 10px 20px;
  border-radius: 6px;
  transition: all 0.3s ease;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 120px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1),
    0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.compare-button:hover {
  background: linear-gradient(to right, #047857, #059669);
  box-shadow: 0 6px 8px -2px rgba(0, 0, 0, 0.15),
    0 3px 6px -2px rgba(0, 0, 0, 0.1);
  transform: translateY(-1px);
}

.compare-button:active {
  transform: translateY(0);
  box-shadow: 0 2px 4px -1px rgba(0, 0, 0, 0.1),
    0 1px 2px -1px rgba(0, 0, 0, 0.06);
}
</style>
  