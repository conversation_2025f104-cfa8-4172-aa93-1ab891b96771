<!-- 代码已包含 CSS：使用 TailwindCSS , 安装 TailwindCSS 后方可看到布局样式效果 -->

<template>
  <div
    class="bg-grid min-h-screen flex flex-col items-center justify-center relative tech-grid"
  > 

   <!--logo-->
   <Logo class="absolute top-8 left-8 z-40 sm:top-8 left-4 sm:left-8 z-40 transform scale-75 sm:scale-100" />

   <UserInfoBar class="absolute bottom-4 sm:bottom-6 md:bottom-8 right-4 sm:right-6 md:right-8 z-40 text-xs sm:text-sm md:text-base" />

    <!-- 返回按钮 -->
    <div class="absolute top-4 sm:top-6 md:top-8 right-4 sm:right-6 md:right-8 z-50 flex gap-2 sm:gap-3 md:gap-4">
      <!-- 使用HomeButton组件替代原来的返回主页按钮 -->
      <!-- <HomeButton/> -->

      <!-- 使用LogoutButton组件替代原来的返回登录按钮 -->
      <LogoutButton @confirm="onLogoutSuccess" />
    </div>
    
    <!--返回登录页-->
    <!-- 返回登录页按钮 -->
 



    <!-- 标题 -->
    <h1 class="text-white text-xl sm:text-2xl md:text-3xl mb-6 sm:mb-10 md:mb-16">请选择业务类型</h1>

    <!-- 主要内容区 -->
     <div  v-if="isRestrictedUser" class="w-full px-4">
      <div
          class="flex flex-wrap gap-8 justify-center items-center"
        >
          <div
            @click="goSave"
            class="tech-card w-64 sm:w-72 md:w-80 h-64 sm:h-72 md:h-80 flex flex-col items-center justify-center p-8 rounded-lg group"
          >
            <div class="text-4xl sm:text-5xl md:text-6xl text-primary mb-6">
              <i class="fas fa-file-signature"></i>
            </div>
            <span class="text-lg sm:text-xl text-white font-mono tracking-wider"
              >存证入口</span
            >
            <div
              class="mt-4 w-16 h-1 bg-gradient-to-r from-primary to-secondary"
            ></div>
          </div>

          <div
            @click="goPick"
            class="tech-card w-64 sm:w-72 md:w-80 h-64 sm:h-72 md:h-80 flex flex-col items-center justify-center p-8 rounded-lg group"
          >
            <div class="text-4xl sm:text-5xl md:text-6xl text-primary mb-6">
              <i class="fas fa-search"></i>
            </div>
            <span class="text-lg sm:text-xl text-white font-mono tracking-wider"
              >取证入口</span
            >
            <div
              class="mt-4 w-16 h-1 bg-gradient-to-r from-primary to-secondary"
            ></div>
          </div>
          
        </div>
     </div>

    <div
      v-if="!isRestrictedUser"
      class="flex items-center justify-center space-x-4 sm:space-x-8 md:space-x-16 relative w-full px-4"
      @touchstart="handleTouchStart"
      @touchmove="handleTouchMove"
      @touchend="handleTouchEnd"
    >
      <!-- 左侧箭头 -->
      <div
        v-if="!isRestrictedUser"
        class="text-blue-500/60 text-2xl sm:text-3xl md:text-4xl animate-pulse cursor-pointer hover:text-blue-500 transition-colors z-10 px-2 sm:px-4"
        @click.stop="prevContainer"
      >
        <i class="fas fa-chevron-left"></i>
      </div>

      <!-- 内容区域 -->
      <div
        class="content-wrapper"
        :style="{ transform: `translateX(${touchOffset}px)` }"
      >
        <!-- 存证入口和取证入口 -->
        <div
          class="flex flex-wrap gap-4 sm:gap-6 md:gap-8 justify-center items-center"
          v-show="currentContainerIndex === 0"
        >
          <div
            @click="goSave"
            class="tech-card w-64 sm:w-72 md:w-80 h-64 sm:h-72 md:h-80 flex flex-col items-center justify-center p-8 rounded-lg group"
          >
            <div class="text-4xl sm:text-5xl md:text-6xl text-primary mb-6">
              <i class="fas fa-file-signature"></i>
            </div>
            <span class="text-lg sm:text-xl text-white font-mono tracking-wider"
              >存证入口</span
            >
            <div
              class="mt-4 w-16 h-1 bg-gradient-to-r from-primary to-secondary"
            ></div>
          </div>

          <div
            @click="goPick"
            :class="{'hidden': screenWidth < 640 && cardsPerPage === 1}"
            class="tech-card w-64 sm:w-72 md:w-80 h-64 sm:h-72 md:h-80 flex flex-col items-center justify-center p-8 rounded-lg group"
          >
            <div class="text-4xl sm:text-5xl md:text-6xl text-primary mb-6">
              <i class="fas fa-search"></i>
            </div>
            <span class="text-lg sm:text-xl text-white font-mono tracking-wider"
              >取证入口</span
            >
            <div
              class="mt-4 w-16 h-1 bg-gradient-to-r from-primary to-secondary"
            ></div>
          </div>
          
        </div>
         
        <!-- 取证入口（小屏幕模式） -->
        <div
          v-if="!isRestrictedUser && screenWidth < 640 && cardsPerPage === 1"
          class="flex flex-wrap gap-4 sm:gap-6 md:gap-8 justify-center items-center"
          v-show="currentContainerIndex === 1"
        >
          <div
            @click="goPick"
            class="tech-card w-64 sm:w-72 md:w-80 h-64 sm:h-72 md:h-80 flex flex-col items-center justify-center p-8 rounded-lg group"
          >
            <div class="text-4xl sm:text-5xl md:text-6xl text-primary mb-6">
              <i class="fas fa-search"></i>
            </div>
            <span class="text-lg sm:text-xl text-white font-mono tracking-wider"
              >取证入口</span
            >
            <div
              class="mt-4 w-16 h-1 bg-gradient-to-r from-primary to-secondary"
            ></div>
          </div>
        </div>

        <!-- 用户管理 -->
        <div
          v-if="!isRestrictedUser"
          class="flex flex-wrap gap-4 sm:gap-6 md:gap-8 justify-center items-center"
          v-show="(screenWidth >= 640 && currentContainerIndex === 1) || (screenWidth < 640 && currentContainerIndex === 2)"
        >
          <div
            @click="goUserManage"
            class="tech-card w-64 sm:w-72 md:w-80 h-64 sm:h-72 md:h-80 flex flex-col items-center justify-center p-8 rounded-lg group"
          >
            <div class="text-4xl sm:text-5xl md:text-6xl text-primary mb-6">
              <i class="fas fa-users"></i>
            </div>
            <span class="text-lg sm:text-xl text-white font-mono tracking-wider"
              >用户管理</span
            >
            <div
              class="mt-4 w-16 h-1 bg-gradient-to-r from-primary to-secondary"
            ></div>
          </div>

          <div
            @click="goGroupManage"
            :class="{'hidden': screenWidth < 640 && cardsPerPage === 1}"
            class="tech-card w-64 sm:w-72 md:w-80 h-64 sm:h-72 md:h-80 flex flex-col items-center justify-center p-8 rounded-lg group"
          >
            <div class="text-4xl sm:text-5xl md:text-6xl text-primary mb-6">
              <i class="fas fa-search"></i>
            </div>
            <span class="text-lg sm:text-xl text-white font-mono tracking-wider"
              >班组管理</span
            >
            <div
              class="mt-4 w-16 h-1 bg-gradient-to-r from-primary to-secondary"
            ></div>
          </div>
        </div>

        <!-- 班组管理（小屏幕模式） -->
        <div
          v-if="!isRestrictedUser && screenWidth < 640 && cardsPerPage === 1"
          class="flex flex-wrap gap-4 sm:gap-6 md:gap-8 justify-center items-center"
          v-show="currentContainerIndex === 3"
        >
          <div
            @click="goGroupManage"
            class="tech-card w-64 sm:w-72 md:w-80 h-64 sm:h-72 md:h-80 flex flex-col items-center justify-center p-8 rounded-lg group"
          >
            <div class="text-4xl sm:text-5xl md:text-6xl text-primary mb-6">
              <i class="fas fa-search"></i>
            </div>
            <span class="text-lg sm:text-xl text-white font-mono tracking-wider"
              >班组管理</span
            >
            <div
              class="mt-4 w-16 h-1 bg-gradient-to-r from-primary to-secondary"
            ></div>
          </div>
        </div>

        <!-- 班表管理 -->
        <div
          v-if="!isRestrictedUser"
          class="flex flex-wrap gap-4 sm:gap-6 md:gap-8 justify-center items-center"
          v-show="(screenWidth >= 640 && currentContainerIndex === 2) || (screenWidth < 640 && currentContainerIndex === 4)"
        >
          <div
            @click="goScheduling"
            class="tech-card w-64 sm:w-72 md:w-80 h-64 sm:h-72 md:h-80 flex flex-col items-center justify-center p-8 rounded-lg group"
          >
            <div class="text-4xl sm:text-5xl md:text-6xl text-primary mb-6">
              <i class="fas fa-calendar-alt"></i>
            </div>
            <span class="text-lg sm:text-xl text-white font-mono tracking-wider"
              >班表管理</span
            >
            <div
              class="mt-4 w-16 h-1 bg-gradient-to-r from-primary to-secondary"
            ></div>
          </div>
        </div>
      </div>

      <!-- 指示器 -->
      <div
        v-if="!isRestrictedUser"
        class="container-indicator absolute -bottom-16"
      >
        <div class="flex space-x-2">
          <div
            v-for="(_, index) in totalIndicators"
            :key="index"
            class="w-2 h-2 rounded-full transition-all duration-300"
            :class="
              index === currentContainerIndex
                ? 'bg-blue-500 w-6'
                : 'bg-blue-500/30'
            "
            @click="goToContainer(index)"
          ></div>
        </div>
      </div>

      <!-- 右侧箭头 -->
      <div
        v-if="!isRestrictedUser"
        class="text-blue-500/60 text-2xl sm:text-3xl md:text-4xl animate-pulse cursor-pointer hover:text-blue-500 transition-colors"
        @click="nextContainer"
      >
        <i class="fas fa-chevron-right"></i>
      </div>

    </div>

  </div>
</template>

<script>
 import UserInfoBar from '@/components/UserInfoBar.vue';
 import LogoutButton from '@/components/LogoutButton.vue';
import Logo from '@/components/Logo.vue';
export default {
  name: "actionType",
  components: {
    UserInfoBar,
    LogoutButton,
    Logo
  },
  data() {
    return {
      offline:0,
      // 当前显示的容器索引
      currentContainerIndex: 0,
      // 容器总数
      totalContainers: 3,
      // 触摸相关数据
      touchStartX: 0,
      touchEndX: 0,
      touchOffset: 0,
      // 最小滑动距离（像素）
      minSwipeDistance: 50,
      // 路由参数
      routeParams: {},
      isRestrictedUser: true,
      // 是否正在滑动
      isSliding: false,
      // 上次点击时间
      lastClickTime: 0,
      // 当前时间
      currentTime: '',
      // 时间更新定时器
      timeInterval: null,
      // 屏幕宽度
      screenWidth: window.innerWidth,
      // 每页显示的卡片数量
      cardsPerPage: 2,
    };
  },
  created() {
    // 在组件创建时获取路由参数
    this.getRouteParams();
    // 初始化时间
    this.updateCurrentTime();
    // 设置定时器，每秒更新一次时间
    this.timeInterval = setInterval(this.updateCurrentTime, 1000);
    // 添加窗口大小变化监听
    window.addEventListener('resize', this.handleResize);
    // 初始化屏幕尺寸
    this.handleResize();
  },
  beforeDestroy() {
    // 组件销毁前清除定时器
    if (this.timeInterval) {
      clearInterval(this.timeInterval);
    }
    // 移除窗口大小变化监听
    window.removeEventListener('resize', this.handleResize);
  },
  computed: {
    // 根据屏幕宽度和卡片每页数量计算总指示器数量
    totalIndicators() {
      // 实际卡片总数：存证入口、取证入口、用户管理、班组管理、班表管理
      const totalCards = 5;
      
      if (this.screenWidth < 640 && this.cardsPerPage === 1) {
        // 小屏幕一页一个卡片时，计算实际需要的页数
        return totalCards;
      }
      return this.totalContainers;
    }
  },
  inject: ["speak", "websocketService", "websocketMsg"],
  watch: {
    // 监听路由参数变化
    '$route': {
      handler(to, from) {
        console.log('路由参数变化:', from, '->', to);
        this.getRouteParams();
      },
      deep: true
    },
    websocketMsg: {
      handler(newMessage) {
        console.log("收到新的WebSocket消息系统设置:", newMessage);
        if (newMessage) {
          this.handleSettingMessages(newMessage);
        }
      },
      deep: true,
      immediate: true,
    },
  },
  methods: {
    // 处理窗口大小变化
    handleResize() {
      this.screenWidth = window.innerWidth;
      // 根据屏幕宽度设置每页卡片数量
      if (this.screenWidth < 640) {
        this.cardsPerPage = 1; // 小屏幕一页显示一个卡片
      } else {
        this.cardsPerPage = 2; // 大屏幕一页显示两个卡片
      }
    },
    // 跳转到指定容器
    goToContainer(index) {
      if (this.isRestrictedUser) return;
      this.currentContainerIndex = index;
    },
    loadSystemSettings() {
      //请求获取服务端配置项
      let order = {
        type: 10031,
        id: "loadSystemSettings",
      };
      this.websocketService.send(order);
    },
    handleSettingMessages(message) {
      if (!message) return;
      // 根据消息类型处理不同的情况
      if (message && message.messages) {
        console.log("系统设置消息:", message, message.messages.type);
        switch (message.messages.type) {
          case 20031: //获取服务端配置项响应
            this.offline = message.messages.data.offline;
          console.log("offline",this.offline)

              // 检查是否是受限用户（roleType === 3）
        if (this.routeParams.data && this.routeParams.data.roleType === 3 || (this.routeParams.data.roleType === 2 && this.offline === 1) || (this.routeParams.data.roleType === 1 && this.offline === 1) ) {
          this.isRestrictedUser = true;
          this.currentContainerIndex = 0; // 强制显示第一个容器
          this.totalContainers = 1; // 只有一个容器可用
        } else {

          this.isRestrictedUser = false;
          console.log("用户不是受限用户，可以访问所有功能");
        }
            break;
       

          default:
          // console.log("未处理的消息类型:", message);
        }
      }
    },
    // 更新当前时间
    updateCurrentTime() {
      const now = new Date();
      const year = now.getFullYear();
      const month = String(now.getMonth() + 1).padStart(2, '0');
      const day = String(now.getDate()).padStart(2, '0');
      const hours = String(now.getHours()).padStart(2, '0');
      const minutes = String(now.getMinutes()).padStart(2, '0');
      const seconds = String(now.getSeconds()).padStart(2, '0');
      this.currentTime = `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
    },
    // 获取用户名称
    getUserName() {
      if (this.routeParams && this.routeParams.data && this.routeParams.data.account) {
        return this.routeParams.data.account;
      }
      return '未登录用户';
    },

    // 获取路由参数
    getRouteParams() {
      try {
        // 获取路由参数
        this.routeParams = this.$route.params || {};
        // console.log("获取到的路由参数:", JSON.stringify(this.routeParams));
        
        // 从本地存储获取 userInfo 信息
        const localUserInfo = localStorage.getItem('userInfo');

        console.log("localUserInfo",localUserInfo)
        
        if (localUserInfo) {
          const userInfo = JSON.parse(localUserInfo);
          // console.log("获取到的本地用户信息:", JSON.stringify(userInfo));


          
          
          // 如果路由参数中没有data或data不完整，则使用本地存储的用户信息
          if (!this.routeParams.data || Object.keys(this.routeParams.data).length === 0) {
            this.routeParams.data = userInfo;
          } else {
            // 合并路由参数和本地存储的用户信息，以路由参数为优先
            this.routeParams.data = { ...userInfo, ...this.routeParams.data };
          }
        }


        this.loadSystemSettings();

      
      } catch (error) {
        console.error("获取路由参数或本地用户信息时出错:", error);
        this.routeParams = {};
      }
    },
    // 处理点击事件的通用方法
    handleClick(callback) {
      const now = new Date().getTime();
      // 防止快速连续点击（300ms内）
      if (now - this.lastClickTime < 300 || this.isSliding) {
        console.log("点击过于频繁或正在滑动，忽略此次点击");
        return;
      }
      this.lastClickTime = now;
      callback();
    },
    // 返回首页

    
    // 退出登录成功的回调
    onLogoutSuccess() {
      console.log("退出登录成功，已在LogoutButton组件中处理");
      // 如果需要额外处理，可以在这里添加代码
    },
    
    goPick() {
      this.handleClick(() => {
        console.log("跳转到取证入口，路由参数:", this.routeParams);
        this.$router.push({
          name: "pick",
          params: this.routeParams && this.routeParams.data ? this.routeParams.data : {}
        });
      });
    },
    goSave(){
      this.handleClick(() => {
        console.log("跳转到存证入口，路由参数:", this.routeParams);
        this.$router.push({
          name: "save",
          params: this.routeParams && this.routeParams.data ? this.routeParams.data : {}
        });
      });
    },
    goUserManage(){
      this.handleClick(() => {
        console.log("跳转到用户管理，路由参数:", this.routeParams);
        this.$router.push({
          name: "userManage",
          params: this.routeParams && this.routeParams.data ? this.routeParams.data : {}
        });
      });
    },
    goGroupManage(){
      this.handleClick(() => {
        console.log("跳转到班组管理，路由参数:", this.routeParams);
        this.$router.push({
          name: "groupManage",
          params: this.routeParams && this.routeParams.data ? this.routeParams.data : {}
        });
      });
    },
    goScheduling(){
      this.handleClick(() => {
        console.log("跳转到班表管理，路由参数:", this.routeParams);
        this.$router.push({
          name: "scheduling",
          params: this.routeParams && this.routeParams.data ? this.routeParams.data : {}
        });
      });
    },
    
    

    // 切换到前一个容器
    prevContainer(event) {
      if (event) {
        event.stopPropagation(); // 阻止事件冒泡
      }
      if (this.isRestrictedUser) return; // 受限用户不允许切换容器
      
      if (this.currentContainerIndex > 0) {
        this.currentContainerIndex--;
      } else {
        // 循环到最后一个
        this.currentContainerIndex = this.totalIndicators - 1;
      }
    },

    // 切换到下一个容器
    nextContainer() {
      if (this.isRestrictedUser) return; // 受限用户不允许切换容器
      
      if (this.currentContainerIndex < this.totalIndicators - 1) {
        this.currentContainerIndex++;
      } else {
        // 循环到第一个
        this.currentContainerIndex = 0;
      }
    },

    // 触摸开始事件处理
    handleTouchStart(event) {
      if (this.isRestrictedUser) return; // 受限用户不允许滑动
      this.touchStartX = event.touches[0].clientX;
      this.touchEndX = this.touchStartX; // 初始化touchEndX
      this.touchOffset = 0;
      this.isSliding = true; // 标记开始滑动
    },

    // 触摸移动事件处理
    handleTouchMove(event) {
      if (this.isRestrictedUser) return; // 受限用户不允许滑动
      const currentX = event.touches[0].clientX;
      this.touchEndX = currentX; // 更新touchEndX
      
      // 限制滑动距离，防止过度拖动
      const maxOffset = this.screenWidth * 0.3; // 最大滑动距离为屏幕宽度的30%
      const rawOffset = currentX - this.touchStartX;
      
      if (Math.abs(rawOffset) > maxOffset) {
        // 如果超过最大滑动距离，则限制在最大值
        this.touchOffset = Math.sign(rawOffset) * maxOffset;
      } else {
        this.touchOffset = rawOffset;
      }
    },

    // 触摸结束事件处理
    handleTouchEnd() {
      if (this.isRestrictedUser) return; // 受限用户不允许滑动
      
      // 计算滑动距离
      const swipeDistance = this.touchEndX - this.touchStartX;
      
      // 根据屏幕宽度调整最小滑动距离
      const adjustedMinSwipeDistance = Math.min(this.screenWidth * 0.1, this.minSwipeDistance);
      
      console.log("滑动距离:", swipeDistance, "最小滑动距离:", adjustedMinSwipeDistance);

      if (Math.abs(swipeDistance) > adjustedMinSwipeDistance) {
        if (swipeDistance > 0) {
          // 向右滑动，显示上一个
          this.prevContainer();
        } else {
          // 向左滑动，显示下一个
          this.nextContainer();
        }
      }

      // 重置触摸偏移
      this.touchOffset = 0;
      
      // 添加短暂延时，防止滑动后立即触发点击事件
      if (Math.abs(swipeDistance) > 10) {
        const cards = document.querySelectorAll('.tech-card');
        cards.forEach(card => {
          card.style.pointerEvents = 'none';
          setTimeout(() => {
            card.style.pointerEvents = 'auto';
          }, 300);
        });
        
        // 延迟重置滑动状态
        setTimeout(() => {
          this.isSliding = false;
        }, 300);
      } else {
        this.isSliding = false; // 立即重置滑动状态
      }
    },
  },
};
</script>

<style scoped>
.bg-grid {
  background-color: #1c1f37;
  background-image: linear-gradient(
      rgba(255, 255, 255, 0.05) 1px,
      transparent 1px
    ),
    linear-gradient(90deg, rgba(255, 255, 255, 0.05) 1px, transparent 1px);
  background-size: 20px 20px;
}

/* 用户信息栏样式 */
.user-info-bar {
  background: rgba(28, 31, 55, 0.7);
  border-radius: 4px;
  padding: 8px 12px;
}

.content-wrapper {
  transition: transform 0.3s ease-out;
  will-change: transform;
  width: 100%;
  max-width: 100vw;
  overflow-x: hidden;
}

.entry-card {
  width: 100%;
  max-width: 280px;
  height: auto;
  aspect-ratio: 1/1;
  border: 2px solid #4169e1;
  border-radius: 4px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
}

.entry-card:hover {
  transform: scale(1.02);
  border-color: #6495ed;
}

.entry-icon-wrapper {
  color: #4169e1;
  margin-bottom: 20px;
}

.entry-text {
  color: white;
  font-size: 1.25rem;
  margin-bottom: 10px;
}

.entry-line {
  width: 40px;
  height: 2px;
  background-color: #4169e1;
  transition: all 0.3s ease;
}

.entry-card:hover .entry-line {
  width: 60px;
  background-color: #6495ed;
}

/* 添加容器切换的过渡效果 */
.container-enter-active,
.container-leave-active {
  transition: opacity 0.5s ease, transform 0.5s ease;
}
.container-enter-from,
.container-leave-to {
  opacity: 0;
  transform: translateX(-30px);
}

@keyframes glow {
  0% {
    box-shadow: 0 0 5px #0066ff;
  }
  50% {
    box-shadow: 0 0 20px #0066ff;
  }
  100% {
    box-shadow: 0 0 5px #0066ff;
  }
}

@keyframes borderFlow {
  0% {
    border-image-source: linear-gradient(0deg, #0066ff, #00a3ff);
  }
  50% {
    border-image-source: linear-gradient(180deg, #0066ff, #00a3ff);
  }
  100% {
    border-image-source: linear-gradient(360deg, #0066ff, #00a3ff);
  }
}

.tech-card {
  background: linear-gradient(
    135deg,
    rgba(0, 102, 255, 0.1),
    rgba(0, 163, 255, 0.1)
  );
  border: 2px solid;
  border-image-slice: 1;
  border-image-source: linear-gradient(45deg, #0066ff, #00a3ff);
  transition: all 0.3s ease;
}

.tech-card:hover {
  animation: glow 2s infinite;
  transform: scale(0.95);
}

.tech-grid {
  background-image: linear-gradient(rgba(0, 102, 255, 0.1) 1px, transparent 1px),
    linear-gradient(90deg, rgba(0, 102, 255, 0.1) 1px, transparent 1px);
  background-size: 20px 20px;
}

/* 响应式设计 */
@media (max-width: 640px) {
  .tech-card {
    width: 100%;
    max-width: 250px;
    height: auto;
    aspect-ratio: 1/1;
  }
  
  h1 {
    font-size: 1.5rem;
    margin-bottom: 1rem;
  }
}

@media (min-width: 641px) and (max-width: 1024px) {
  .tech-card {
    width: 100%;
    max-width: 280px;
  }
  
  h1 {
    font-size: 2rem;
    margin-bottom: 2rem;
  }
}

@media (min-width: 1025px) {
  .tech-card {
    width: 100%;
    max-width: 320px;
  }
  
  h1 {
    font-size: 2.5rem;
    margin-bottom: 3rem;
  }
}
</style>

