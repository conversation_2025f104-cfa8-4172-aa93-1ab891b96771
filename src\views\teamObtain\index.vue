<!-- 代码已包�?CSS：使�?TailwindCSS , 安装 TailwindCSS 后方可看到布局样式效果 -->

<template>
  <div class="min-h-screen tech-grid bg-gray-900">
    <!-- 返回按钮区域 -->
    <div class="absolute top-8 right-8 z-50 flex gap-4">
      <!-- 使用HomeButton组件替代原来的返回主页按钮 -->
      <HomeButton />
      <!-- 使用LogoutButton组件替代原来的返回登录按钮 -->
      <LogoutButton />
    </div>

    <div class="container mx-auto px-4 h-screen flex flex-col items-center justify-center">
      <!-- 主要内容卡片 -->
      <div class="tech-card p-8 w-full max-w-lg">
        <!-- 选项卡 -->
        <div class="flex mb-6">
          <button
            @click="activeTab = 'personal'"
            :class="[
              'flex-1 py-3 px-4 text-center font-medium transition-all duration-300 text-sm',
              activeTab === 'personal'
                ? 'bg-blue-500 text-white rounded-l-lg'
                : 'bg-gray-700 text-gray-300 hover:bg-gray-600 rounded-l-lg'
            ]"
          >
            本人取证
          </button>
          <button
            @click="activeTab = 'team'"
            :class="[
              'flex-1 py-3 px-4 text-center font-medium transition-all duration-300 text-sm',
              activeTab === 'team'
                ? 'bg-blue-500 text-white'
                : 'bg-gray-700 text-gray-300 hover:bg-gray-600'
            ]"
          >
            整组取证
          </button>
          <button
            @click="activeTab = 'partial'"
            :class="[
              'flex-1 py-3 px-4 text-center font-medium transition-all duration-300 text-sm',
              activeTab === 'partial'
                ? 'bg-blue-500 text-white rounded-r-lg'
                : 'bg-gray-700 text-gray-300 hover:bg-gray-600 rounded-r-lg'
            ]"
          >
            选择取证
          </button>
        </div>

        <!-- 选择部分取证内容 -->
        <div v-if="activeTab === 'partial'" class="space-y-6">
          <!-- 搜索框 -->
          <div class="relative">
            <input
              v-model="searchQuery"
              type="text"
              placeholder="输入姓名搜索人员..."
              class="w-full p-4 bg-gray-800 border-2 border-gray-600 rounded-lg text-white focus:border-blue-500 focus:outline-none"
            />
            <div class="absolute top-4 right-4 pointer-events-none">
              <svg class="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
              </svg>
            </div>
          </div>

          <!-- 人员列表 -->
          <div class="bg-gray-800 border-2 border-gray-600 rounded-lg p-4 max-h-60 overflow-y-auto">
            <div class="grid grid-cols-1 gap-2">
              <div
                v-for="member in filteredMembers"
                :key="member.id"
                @click="toggleMember(member.account)"
                :class="[
                  'p-3 rounded-lg cursor-pointer transition-all duration-300 flex items-center justify-between',
                  evidenceAccountList.includes(member.account)
                    ? 'bg-blue-600 text-white'
                    : 'bg-gray-700 hover:bg-gray-600 text-gray-300'
                ]"
              >
                <div class="flex items-center gap-3">
                  <div
                    :class="[
                      'w-5 h-5 rounded border-2 flex items-center justify-center',
                      evidenceAccountList.includes(member.account)
                        ? 'bg-white border-white'
                        : 'border-gray-400'
                    ]"
                  >
                    <svg
                      v-if="evidenceAccountList.includes(member.account)"
                      class="w-3 h-3 text-blue-600"
                      fill="currentColor"
                      viewBox="0 0 20 20"
                    >
                      <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                    </svg>
                  </div>
                  <div>
                    <div class="font-medium">{{ member.name }}</div>
                    <div class="text-sm opacity-75">{{ member.department }}</div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 无搜索结果提示 -->
            <div v-if="filteredMembers.length === 0" class="text-center py-8 text-gray-400">
              <p>未找到匹配的人员</p>
            </div>
          </div>

          <!-- 快捷操作按钮 -->
          <div class="flex gap-2 items-center">
            <button
              @click="selectAll"
              class="px-4 py-2 bg-green-600 hover:bg-green-700 text-white rounded-lg text-sm transition-all duration-300"
            >
              全选
            </button>
            <button
              @click="clearAll"
              class="px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-lg text-sm transition-all duration-300"
            >
              清空
            </button>
            <span class="text-gray-400 text-sm">
              已选择 {{ evidenceAccountList.length }} / {{ teamMembers.length }} 人
            </span>
          </div>

          <!-- 已选择人员显示 -->
          <div v-if="evidenceAccountList.length > 0" class="bg-gray-800 p-4 rounded-lg">
            <p class="text-gray-300 mb-2">已选择人员：</p>
            <div class="flex flex-wrap gap-2">
              <span
                v-for="account in evidenceAccountList"
                :key="account"
                class="member-tag bg-blue-600 text-white px-3 py-1 rounded-full text-sm flex items-center gap-2"
              >
                {{ getTeamMemberNameByAccount(account) }}
                <button
                  @click="removeMember(account)"
                  class="hover:bg-blue-700 rounded-full w-4 h-4 flex items-center justify-center"
                >
                  ×
                </button>
              </span>
            </div>
          </div>
        </div>

        <!-- 本人取证内容 -->
        <div v-if="activeTab === 'personal'" class="text-center py-8">
          <p class="text-gray-300 mb-4">本人取证模式</p>
          <p class="text-sm text-gray-400">点击确定按钮开始本人取证流程</p>
        </div>

        <!-- 整组取证内容 -->
        <div v-if="activeTab === 'team'" class="text-center py-8">
          <p class="text-gray-300 mb-4">整组取证模式</p>
          <p class="text-sm text-gray-400">将对整个班组所有人员进行取证</p>
        </div>

        <!-- 确定按钮 -->
        <button
          @click="handleConfirm"
          :disabled="activeTab === 'partial' && evidenceAccountList.length === 0"
          :class="[
            'w-full py-4 px-6 rounded-lg font-medium text-lg transition-all duration-300',
            (activeTab === 'personal' || activeTab === 'team' || (activeTab === 'partial' && evidenceAccountList.length > 0))
              ? 'bg-blue-500 hover:bg-blue-600 text-white cursor-pointer'
              : 'bg-gray-600 text-gray-400 cursor-not-allowed'
          ]"
        >
          确定
        </button>
      </div>
    </div>
  </div>
</template>

<script>
import LogoutButton from "@/components/LogoutButton.vue";
import HomeButton from "@/components/HomeButton.vue";

export default {
  name: "TeamObtain",
  components: {
    LogoutButton,
    HomeButton,
  },
  inject: ["speak", "websocketService", "websocketMsg", "speakSync"],
  data() {
    return {
      activeTab: 'personal', // 当前激活的选项卡：'personal'、'team' 或 'partial'
      evidenceAccountList: [], // 取证账户列表
      searchQuery: '', // 搜索查询字符串
      teamMembers: [
        // 模拟团队成员数据
        { id: 1, name: '张三', department: '技术部', account: 'zhangsan' },
        { id: 2, name: '李四', department: '技术部', account: 'lisi' },
        { id: 3, name: '王五', department: '运维部', account: 'wangwu' },
        { id: 4, name: '赵六', department: '运维部', account: 'zhaoliu' },
        { id: 5, name: '钱七', department: '安全部', account: 'qianqi' },
        { id: 6, name: '孙八', department: '安全部', account: 'sunba' },
        { id: 7, name: '周九', department: '技术部', account: 'zhoujiu' },
        { id: 8, name: '吴十', department: '运维部', account: 'wushi' },
      ],
    };
  },
  computed: {
    // 根据搜索查询过滤人员
    filteredMembers() {
      if (!this.searchQuery.trim()) {
        return this.teamMembers;
      }
      return this.teamMembers.filter(member =>
        member.name.toLowerCase().includes(this.searchQuery.toLowerCase()) ||
        member.department.toLowerCase().includes(this.searchQuery.toLowerCase())
      );
    }
  },
  watch: {
    // 当切换选项卡时，清空已选择的成员和搜索查询
    activeTab() {
      this.evidenceAccountList = [];
      this.searchQuery = '';
    }
  },
  methods: {
    // 获取团队成员姓名（通过ID）
    getTeamMemberName(memberId) {
      const member = this.teamMembers.find(m => m.id === memberId);
      return member ? member.name : '';
    },

    // 获取团队成员姓名（通过账户）
    getTeamMemberNameByAccount(account) {
      const member = this.teamMembers.find(m => m.account === account);
      return member ? member.name : '';
    },

    // 切换成员选择状态
    toggleMember(account) {
      const index = this.evidenceAccountList.indexOf(account);
      if (index > -1) {
        // 如果已选中，则取消选择
        this.evidenceAccountList.splice(index, 1);
        const memberName = this.getTeamMemberNameByAccount(account);
        this.speak(`取消选择 ${memberName}`);
      } else {
        // 如果未选中，则添加选择
        this.evidenceAccountList.push(account);
        const memberName = this.getTeamMemberNameByAccount(account);
        this.speak(`选择 ${memberName}`);
      }
    },

    // 移除选中的成员
    removeMember(account) {
      this.evidenceAccountList = this.evidenceAccountList.filter(acc => acc !== account);
      const memberName = this.getTeamMemberNameByAccount(account);
      this.speak(`移除 ${memberName}`);
    },

    // 全选当前显示的成员
    selectAll() {
      // 获取当前过滤结果中未选择的成员账户
      const filteredAccounts = this.filteredMembers.map(member => member.account);
      const newSelections = filteredAccounts.filter(account => !this.evidenceAccountList.includes(account));

      // 添加到已选择列表
      this.evidenceAccountList = [...this.evidenceAccountList, ...newSelections];

      if (this.searchQuery.trim()) {
        this.speak(`已选择当前搜索结果中的所有人员，共${newSelections.length}人`);
      } else {
        this.speak(`已全选所有人员，共${this.teamMembers.length}人`);
      }
    },

    // 清空所有选择
    clearAll() {
      this.evidenceAccountList = [];
      this.searchQuery = '';
      this.speak("已清空所有选择");
    },

    // 处理确定按钮点击
    handleConfirm() {
      let order = {
        type: 10009,
        id: "evidence",
        data: {}
      };

      if (this.activeTab === 'personal') {
        // 本人取证：isGroupEvidence 为 false
        this.speak("开始本人取证");
        order.data = {
          account: 'current_user', // 当前用户账户
          isGroupEvidence: false,
          evidenceAccountList: []
        };
      } else if (this.activeTab === 'team') {
        // 整组取证：isGroupEvidence 为 true
        this.speak("开始整组取证");
        order.data = {
          account: 'current_user', // 当前用户账户
          isGroupEvidence: true,
          evidenceAccountList: this.teamMembers.map(member => member.account)
        };
      } else if (this.activeTab === 'partial') {
        if (this.evidenceAccountList.length === 0) {
          this.speak("请先选择取证人员");
          return;
        }

        // 选择部分取证：不传 isGroupEvidence 字段
        const selectedNames = this.evidenceAccountList.map(account => this.getTeamMemberNameByAccount(account)).join('、');
        this.speak(`开始选择部分取证，选中成员：${selectedNames}`);

        order.data = {
          account: 'current_user', // 当前用户账户
          evidenceAccountList: this.evidenceAccountList
        };
      }

      console.log('发送取证请求:', order);
      this.websocketService.send(order);
    },
  },
};
</script>

<style scoped>
@keyframes glow {
  0% {
    box-shadow: 0 0 5px #0066ff;
  }
  50% {
    box-shadow: 0 0 20px #0066ff;
  }
  100% {
    box-shadow: 0 0 5px #0066ff;
  }
}

@keyframes borderFlow {
  0% {
    border-image-source: linear-gradient(0deg, #0066ff, #00a3ff);
  }
  50% {
    border-image-source: linear-gradient(180deg, #0066ff, #00a3ff);
  }
  100% {
    border-image-source: linear-gradient(360deg, #0066ff, #00a3ff);
  }
}

.tech-card {
  background: linear-gradient(
    135deg,
    rgba(0, 102, 255, 0.1),
    rgba(0, 163, 255, 0.1)
  );
  border: 2px solid;
  border-image-slice: 1;
  border-image-source: linear-gradient(45deg, #0066ff, #00a3ff);
  transition: all 0.3s ease;
  border-radius: 0.5rem;
}

.tech-card:hover {
  animation: glow 2s infinite;
  transform: scale(1.02);
}

.tech-grid {
  background-image: linear-gradient(rgba(0, 102, 255, 0.1) 1px, transparent 1px),
    linear-gradient(90deg, rgba(0, 102, 255, 0.1) 1px, transparent 1px);
  background-size: 20px 20px;
}

/* 选择框样式 */
select {
  transition: all 0.3s ease;
}

select:focus {
  border-color: #0066ff;
  outline: none;
}

/* 按钮悬停效果 */
button:hover:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 102, 255, 0.3);
}

button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* 选中成员标签动画 */
.member-tag {
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: scale(0.8);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}
</style>

