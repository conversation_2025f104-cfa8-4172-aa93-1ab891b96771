<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>软键盘拖拽测试</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
            max-width: 800px;
            margin: 0 auto;
        }
        
        .instructions {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            border-left: 4px solid #007bff;
        }
        
        .test-area {
            border: 2px dashed #ddd;
            padding: 40px;
            text-align: center;
            margin: 20px 0;
            border-radius: 8px;
            background: #fafafa;
        }
        
        input, textarea {
            width: 100%;
            padding: 10px;
            margin: 10px 0;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 16px;
        }
        
        textarea {
            height: 100px;
            resize: vertical;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>软键盘组件拖拽功能测试</h1>
        
        <div class="instructions">
            <h3>测试说明：</h3>
            <ul>
                <li>右下角应该有一个可拖拽的软键盘切换按钮</li>
                <li>可以用鼠标拖拽按钮到屏幕任意位置</li>
                <li>按钮不能被拖拽到屏幕外</li>
                <li>点击按钮可以切换软键盘显示/隐藏状态</li>
                <li>支持触摸设备的拖拽操作</li>
                <li>拖拽时鼠标光标会变为抓取状态</li>
            </ul>
        </div>
        
        <div class="test-area">
            <h3>测试区域</h3>
            <p>在这里测试输入功能和软键盘</p>
            
            <input type="text" placeholder="测试输入框 1">
            <input type="text" placeholder="测试输入框 2">
            <textarea placeholder="测试文本区域"></textarea>
            
            <p>尝试拖拽右下角的软键盘按钮到不同位置</p>
        </div>
        
        <div class="instructions">
            <h3>预期行为：</h3>
            <ul>
                <li>✅ 按钮可以自由拖拽</li>
                <li>✅ 按钮不会超出屏幕边界</li>
                <li>✅ 点击按钮可以切换软键盘</li>
                <li>✅ 拖拽后点击仍然有效</li>
                <li>✅ 窗口大小改变时按钮位置自动调整</li>
            </ul>
        </div>
    </div>

    <!-- 模拟 Vue 和 Element UI 环境 -->
    <script src="https://unpkg.com/vue@2/dist/vue.js"></script>
    <script src="https://unpkg.com/element-ui/lib/index.js"></script>
    <link rel="stylesheet" href="https://unpkg.com/element-ui/lib/theme-chalk/index.css">
    
    <!-- 模拟 Electron API -->
    <script>
        window.electronAPI = {
            openOsk: function() {
                console.log('模拟打开软键盘');
                alert('模拟软键盘已打开');
            },
            closeOsk: function() {
                console.log('模拟关闭软键盘');
                alert('模拟软键盘已关闭');
            }
        };
    </script>
</body>
</html>
